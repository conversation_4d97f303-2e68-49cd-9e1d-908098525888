import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { getUsageColor } from '~/components/styles/MonitorCardStyles';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import * as Clipboard from 'expo-clipboard';

import { useAppStore } from '@/lib/store';
import { SUIConfig, MonitoringStatus } from '@/lib/types';
import { getSUIServerStatus } from '@/panels/s-ui/utils';
import { smartFetch } from '@/lib/utils';
import { router, useLocalSearchParams } from 'expo-router';
import { RefreshCw, ArrowUp, ArrowDown } from 'lucide-react-native';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ScrollView, StyleSheet, View, Alert, TouchableOpacity, SafeAreaView } from 'react-native';
import { Pie, PolarChart, CartesianChart, Area } from 'victory-native';
import { useFocusEffect } from '@react-navigation/native';

// 动画饼图切片组件
function AnimatedPieSlice({ slice }: { slice: any }) {
  return (
    <Pie.Slice
      animate={{
        type: "timing",
        duration: 300,
      }}
    />
  );
}

// 动画饼图组件
interface AnimatedPieChartProps {
  data: Array<{ label: string; value: number; color: string }>;
  size: number;
  innerRadius?: number | string;
}

function AnimatedPieChart({ data, size, innerRadius = "85%" }: AnimatedPieChartProps) {
  return (
    <View style={{ width: size, height: size }}>
      <PolarChart
        data={data}
        labelKey="label"
        valueKey="value"
        colorKey="color"
      >
        <Pie.Chart innerRadius={innerRadius}>
          {({ slice }) => <AnimatedPieSlice slice={slice} />}
        </Pie.Chart>
      </PolarChart>
    </View>
  );
}

export default function SUIOverviewScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const textColor = useThemeColor({}, 'text');

  const { configs, getMonitoringStatus, setMonitoringStatus, getProxyServer } = useAppStore();

  // 直接从configs中计算config，无需useState和useEffect
  const config = useMemo(() => {
    if (!configId) return null;
    return configs.find(c => c.id === configId) as SUIConfig || null;
  }, [configId, configs]);

  const [isRestarting, setIsRestarting] = useState(false);

  // 初始化网络历史数据，始终保持6组数据
  const [networkHistory, setNetworkHistory] = useState<Array<{ up: number; down: number; timestamp: number }>>(() => {
    const initialData = [];
    const now = Date.now();
    for (let i = 0; i < 6; i++) {
      initialData.push({
        up: 0,
        down: 0,
        timestamp: now - (6 - i) * 1500 // 1.5秒间隔
      });
    }
    return initialData;
  });

  // 数据获取相关状态
  const intervalRef = useRef<any>(null);
  const lastUpdateTimeRef = useRef<number>(0);

  // 检查配置有效性
  useEffect(() => {
    if (!configId) {
      Alert.alert(t('common.error'), t('sui.overview.configNotFound'));
      router.back();
      return;
    }

    if (!config) {
      Alert.alert(t('common.error'), t('sui.overview.configNotFound'));
      router.back();
      return;
    }
  }, [configId, config]);

  const status = config ? getMonitoringStatus(config.id) : null;

  // 获取服务器状态
  const fetchServerStatus = async () => {
    if (!config) return;

    const requestTime = Date.now();

    try {
      // 检查是否有中转服务器配置
      const proxyServer = getProxyServer();
      if (proxyServer) {
        // TODO: 中转服务器逻辑暂不实现
        console.log('Proxy server configured but not implemented yet');
        return;
      }

      // 直接获取服务器状态
      const serverStatus = await getSUIServerStatus(config);
      if (!serverStatus) {
        throw new Error('Failed to get server status');
      }

      // 检查是否是最新的请求响应
      if (requestTime < lastUpdateTimeRef.current) {
        console.log('Ignoring outdated response for config:', config.id);
        return;
      }

      // 更新最后更新时间
      lastUpdateTimeRef.current = requestTime;

      const newStatus: MonitoringStatus = {
        isOnline: true,
        lastUpdate: new Date(requestTime).toISOString(),
        failureCount: 0,
        serverStatus,
      };

      setMonitoringStatus(config.id, newStatus);
    } catch (error) {
      console.error('Failed to fetch server status for config:', config.id, error);

      // 检查当前时间是否大于最后更新时间，如果是则设为离线
      if (requestTime > lastUpdateTimeRef.current) {
        // 更新最后更新时间
        lastUpdateTimeRef.current = requestTime;

        const offlineStatus: MonitoringStatus = {
          isOnline: false,
          lastUpdate: new Date(requestTime).toISOString(),
          failureCount: 1,
        };
        setMonitoringStatus(config.id, offlineStatus);
      }
    }
  };

  // 开始轮询
  const startPolling = async () => {
    // 如果已经在轮询或没有配置，直接返回
    if (intervalRef.current || !config) return;

    // 立即获取一次状态
    await fetchServerStatus();

    // 设置轮询间隔 - 每1.5秒轮询一次
    intervalRef.current = setInterval(async () => {
      await fetchServerStatus();
    }, 1500);
  };

  // 停止轮询
  const stopPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  // 使用 useFocusEffect 管理轮询
  useFocusEffect(
    useCallback(() => {
      if (config) {
        startPolling()
      }

      return () => {
        // 当组件失去焦点时停止轮询
        stopPolling();
      };
    }, [config])
  );

  // 更新网络历史数据
  useEffect(() => {
    const serverStatus = status?.serverStatus as any;
    if (serverStatus) {
      // 兼容新旧数据结构
      const upSpeed = serverStatus.net?.psent ?? serverStatus.netIO?.up ?? 0;
      const downSpeed = serverStatus.net?.precv ?? serverStatus.netIO?.down ?? 0;

      if (upSpeed !== undefined && downSpeed !== undefined) {
        const newEntry = {
          up: upSpeed,
          down: downSpeed,
          timestamp: Date.now()
        };

        setNetworkHistory(prev => {
          // 移除第一个元素，添加新数据到末尾，始终保持6组数据
          const updated = [...prev.slice(1), newEntry];
          return updated;
        });
      }
    }
  }, [status?.serverStatus]);

  // 格式化字节数
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 格式化速度
  const formatSpeed = (bytesPerSecond: number): string => {
    return formatBytes(bytesPerSecond) + '/s';
  };

  // 重启确认
  const handleRestart = () => {
    Alert.alert(
      t('sui.overview.confirmRestart'),
      t('sui.overview.confirmRestartMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('sui.overview.restart'),
          onPress: handleRestartSingbox
        }
      ]
    );
  };

  // 重启Sing-box服务
  const handleRestartSingbox = async () => {
    if (!config) return;

    setIsRestarting(true);
    try {
      const baseUrl = `${config.protocol}://${config.url}`;
      const restartUrl = `${baseUrl}/api/restartSb`;

      const response = await smartFetch(restartUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Bearer ${config.api}`,
        },
      }, config);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success) {
        Alert.alert(t('common.success'), t('sui.overview.restartSuccess'));
      } else {
        Alert.alert(t('common.error'), result.msg || t('sui.overview.restartFailed'));
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('sui.overview.restartFailed'));
    } finally {
      setIsRestarting(false);
    }
  };

  // 复制IP地址到剪切板
  const copyIPToClipboard = async (ip: string, version: string) => {
    try {
      await Clipboard.setStringAsync(ip);
      Alert.alert(t('common.success'), t('sui.overview.ipCopySuccess').replace('{version}', version));
    } catch (error) {
      Alert.alert(t('common.error'), t('sui.overview.ipCopyFailed'));
    }
  };

  if (!config) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: textColor }]}>
            {t('sui.overview.loading')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <>
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <ScrollView style={styles.content}>
          {!status || !status.isOnline ? (
            <View style={styles.noDataContainer}>
              <Text style={[styles.noDataText, { color: textColor + '60' }]}>
                No Data
              </Text>
              <Text style={[styles.noDataSubtext, { color: textColor + '40' }]}>
                {status?.isOnline === false
                  ? t('sui.overview.serverOffline')
                  : t('sui.overview.gettingData')}
              </Text>
            </View>
          ) : (
            <>
              {/* Sing-box 状态卡片 */}
              <View style={styles.section}>
                <View style={styles.sectionHeader}>
                  <Text style={[styles.sectionTitle, { color: textColor }]}>
                    {t('sui.overview.singboxStatus')}
                  </Text>
                  <View style={styles.singboxActions}>
                    <TouchableOpacity
                      style={[styles.actionButton, { borderColor }]}
                      onPress={handleRestart}
                      disabled={isRestarting}
                    >
                      <RefreshCw
                        size={16}
                        color={textColor}
                        style={isRestarting ? { opacity: 0.5 } : {}}
                      />
                      <Text style={[styles.actionButtonText, { color: textColor, opacity: isRestarting ? 0.5 : 1 }]}>
                        {isRestarting ? t('sui.overview.restarting') : t('sui.overview.restart')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>

                {status.serverStatus && (
                  <View style={[styles.singboxCard, { backgroundColor: backgroundColor, borderColor }]}>
                    <View style={styles.singboxInfo}>
                      <Badge
                        variant={((status.serverStatus as any).sbd?.running || (status.serverStatus as any).xray?.state === 'running') ? 'default' : 'destructive'}
                        style={styles.singboxStatusBadge}
                      >
                        <Text style={styles.badgeText}>
                          {(status.serverStatus as any).sbd?.running ? 'running' : ((status.serverStatus as any).xray?.state || 'unknown')}
                        </Text>
                      </Badge>
                      <Text style={[styles.singboxVersion, { color: textColor + '80' }]}>
                        {t('sui.overview.version')}: {(status.serverStatus as any).xray?.version || 'N/A'}
                      </Text>
                    </View>
                  </View>
                )}
              </View>

              {/* IP 信息 */}
              {status.serverStatus && (((status.serverStatus as any).sys?.ipv4 && (status.serverStatus as any).sys.ipv4.length > 0) || ((status.serverStatus as any).publicIP)) && (
                <View style={styles.section}>
                  <Text style={[styles.sectionTitle, { color: textColor }]}>
                    IP
                  </Text>
                  <View style={styles.ipRow}>
                    <TouchableOpacity
                      style={styles.ipItem}
                      onPress={() => {
                        const ipv4 = (status.serverStatus as any).sys?.ipv4?.[0]?.split('/')[0] || (status.serverStatus as any).publicIP?.ipv4 || 'N/A';
                        copyIPToClipboard(ipv4, 'IPv4');
                      }}
                    >
                      <Text style={[styles.ipText, { color: textColor }]}>
                        V4: {(status.serverStatus as any).sys?.ipv4?.[0]?.split('/')[0] || (status.serverStatus as any).publicIP?.ipv4 || 'N/A'}
                      </Text>
                    </TouchableOpacity>
                    <Separator orientation="vertical" style={styles.ipSeparator} />
                    <TouchableOpacity
                      style={styles.ipItem}
                      onPress={() => {
                        const ipv6 = (status.serverStatus as any).sys?.ipv6?.[0]?.split('/')[0] || (status.serverStatus as any).publicIP?.ipv6 || 'N/A';
                        copyIPToClipboard(ipv6, 'IPv6');
                      }}
                    >
                      <Text style={[styles.ipText, { color: textColor }]}>
                        V6: {(status.serverStatus as any).sys?.ipv6?.[0]?.split('/')[0] || (status.serverStatus as any).publicIP?.ipv6 || 'N/A'}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}

              {/* 系统资源圆环图 */}
              {status.serverStatus && (
                <View style={styles.section}>
                  <Text style={[styles.sectionTitle, { color: textColor }]}>
                    {t('sui.overview.systemResources')}
                  </Text>

                  {/* 一行显示系统资源图表 */}
                  <View style={styles.systemResourcesRow}>
                    {/* CPU */}
                    <View style={styles.systemResourceItem}>
                      <View style={styles.largePieContainer}>
                        <AnimatedPieChart
                          data={(() => {
                            const cpu = status.serverStatus.cpu;
                            const cpuPercentage = cpu <= 1 ? cpu * 100 : cpu;
                            return [
                              {
                                label: 'Used',
                                value: cpuPercentage,
                                color: getUsageColor(cpuPercentage)
                              },
                              {
                                label: 'Free',
                                value: 100 - cpuPercentage,
                                color: '#e5e7eb'
                              }
                            ];
                          })()}
                          size={100}
                        />
                        <View style={styles.largeChartCenter}>
                          <Text style={[styles.largeChartCenterLabel, { color: textColor }]}>
                            {t('sui.overview.cpu')}
                          </Text>
                          <Text style={[styles.largeChartCenterText, { color: textColor }]}>
                            {(() => {
                              const cpu = status.serverStatus.cpu;
                              const cpuPercentage = cpu <= 1 ? cpu * 100 : cpu;
                              return cpuPercentage.toFixed(1);
                            })()}%
                          </Text>
                        </View>
                      </View>
                      <Text style={[styles.largeChartBottomText, { color: textColor }]}>
                        {(status.serverStatus as any).sys?.cpuCount ?? (status.serverStatus as any).cpuCores ?? 0} cores
                      </Text>
                    </View>

                    {/* 内存 */}
                    <View style={styles.systemResourceItem}>
                      <View style={styles.largePieContainer}>
                        <AnimatedPieChart
                          data={[
                            {
                              label: 'Used',
                              value: (status.serverStatus.mem.current / status.serverStatus.mem.total) * 100,
                              color: getUsageColor((status.serverStatus.mem.current / status.serverStatus.mem.total) * 100)
                            },
                            {
                              label: 'Free',
                              value: 100 - (status.serverStatus.mem.current / status.serverStatus.mem.total) * 100,
                              color: '#e5e7eb'
                            }
                          ]}
                          size={100}
                        />
                        <View style={styles.largeChartCenter}>
                          <Text style={[styles.largeChartCenterLabel, { color: textColor }]}>
                            {t('sui.overview.memory')}
                          </Text>
                          <Text style={[styles.largeChartCenterText, { color: textColor }]}>
                            {((status.serverStatus.mem.current / status.serverStatus.mem.total) * 100).toFixed(1)}%
                          </Text>
                        </View>
                      </View>
                      <Text style={[styles.largeChartBottomText, { color: textColor }]}>
                        {formatBytes(status.serverStatus.mem.current)} / {formatBytes(status.serverStatus.mem.total)}
                      </Text>
                    </View>

                    {/* 磁盘 */}
                    <View style={styles.systemResourceItem}>
                      <View style={styles.largePieContainer}>
                        <AnimatedPieChart
                          data={[
                            {
                              label: 'Used',
                              value: (status.serverStatus.disk.current / status.serverStatus.disk.total) * 100,
                              color: getUsageColor((status.serverStatus.disk.current / status.serverStatus.disk.total) * 100)
                            },
                            {
                              label: 'Free',
                              value: 100 - (status.serverStatus.disk.current / status.serverStatus.disk.total) * 100,
                              color: '#e5e7eb'
                            }
                          ]}
                          size={100}
                        />
                        <View style={styles.largeChartCenter}>
                          <Text style={[styles.largeChartCenterLabel, { color: textColor }]}>
                            {t('sui.overview.disk')}
                          </Text>
                          <Text style={[styles.largeChartCenterText, { color: textColor }]}>
                            {((status.serverStatus.disk.current / status.serverStatus.disk.total) * 100).toFixed(1)}%
                          </Text>
                        </View>
                      </View>
                      <Text style={[styles.largeChartBottomText, { color: textColor }]}>
                        {formatBytes(status.serverStatus.disk.current)} / {formatBytes(status.serverStatus.disk.total)}
                      </Text>
                    </View>
                  </View>
                </View>
              )}

              {/* 网络流量图表 */}
              {status.serverStatus && (
                <View style={styles.section}>
                  <Text style={[styles.sectionTitle, { color: textColor }]}>
                    {t('sui.overview.networkTraffic')}
                  </Text>

                  <View style={styles.networkChartContainer}>
                    <CartesianChart
                      data={networkHistory.map((item, index) => ({
                        x: index,
                        up: item.up / 1024, // 转换为KB/s
                        down: item.down / 1024
                      }))}
                      xKey="x"
                      yKeys={["up", "down"]}

                    >
                      {({ points, chartBounds }) => (
                        <>
                          {/* 下行流量区域图 */}
                          <Area
                            points={points.down}
                            y0={chartBounds.bottom}
                            color="#334D5C"
                            opacity={0.5}
                            animate={{ type: "timing", duration: 300 }}
                          />
                          {/* 上行流量区域图 */}
                          <Area
                            points={points.up}
                            y0={chartBounds.bottom}
                            color="#DF5A49"
                            opacity={0.5}
                            animate={{ type: "timing", duration: 300 }}
                          />
                        </>
                      )}
                    </CartesianChart>
                  </View>

                  {/* 网络统计 - 一行显示 */}
                  <View style={styles.networkStatsRow}>
                    <View style={styles.networkStatGroup}>
                      <ArrowUp size={18} color="#DF5A49" />
                      <Text style={[styles.networkStatText, { color: textColor }]}>
                        {formatSpeed((status.serverStatus as any).net?.psent ?? (status.serverStatus as any).netIO?.up ?? 0)}
                      </Text>
                      <Text style={[styles.networkStatValue, { color: textColor }]}>
                        {formatBytes((status.serverStatus as any).net?.sent ?? (status.serverStatus as any).netTraffic?.sent ?? 0)}
                      </Text>
                    </View>

                    <View style={styles.networkStatGroup}>
                      <ArrowDown size={18} color="#334D5C" />
                      <Text style={[styles.networkStatText, { color: textColor }]}>
                        {formatSpeed((status.serverStatus as any).net?.precv ?? (status.serverStatus as any).netIO?.down ?? 0)}
                      </Text>
                      <Text style={[styles.networkStatValue, { color: textColor }]}>
                        {formatBytes((status.serverStatus as any).net?.recv ?? (status.serverStatus as any).netTraffic?.recv ?? 0)}
                      </Text>
                    </View>
                  </View>
                </View>
              )}
            </>
          )}
        </ScrollView>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  singboxActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    gap: 4,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  noDataText: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 32,
  },
  noDataSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  singboxCard: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  singboxInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  singboxStatusBadge: {
    marginRight: 12,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  singboxVersion: {
    fontSize: 14,
  },
  ipRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 16,
    paddingHorizontal: 8,
    paddingTop: 16
  },
  ipItem: {
    flex: 1,
  },
  ipText: {
    fontSize: 14,
    fontWeight: '500',
  },
  ipSeparator: {
    height: 20,
    width: 1,
  },

  // 系统资源一行显示的样式
  systemResourcesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginTop: 20,
    flexWrap: 'wrap',
  },
  systemResourceItem: {
    alignItems: 'center',
    width: '32%',
  },
  largePieContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
  },
  largeChartCenter: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    width: 110,
    height: 110,
  },
  largeChartCenterLabel: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 2,
  },
  largeChartCenterText: {
    fontSize: 14,
    fontWeight: '700',
    textAlign: 'center',
  },
  largeChartBottomText: {
    fontSize: 11,
    textAlign: 'center',
    marginTop: 4,
    fontWeight: '600',
  },
  networkChartContainer: {
    marginTop: 25,
    height: 150,
    marginBottom: 12,
  },
  networkStatText: {
    fontSize: 14,
    fontWeight: '500',
  },
  networkStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    gap: 16,
  },
  networkStatGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    width: '48%',
  },
  networkStatValue: {
    fontSize: 14,
    fontWeight: '500',
  },
});
